# 删除历史对话接口文档

## 接口概述

新增了删除历史对话的接口，允许用户删除指定的对话记录。

## 接口详情

### 删除对话

**接口路径**: `DELETE /humanrelation/conversation`

**功能**: 删除指定用户的指定对话历史记录

**请求参数**:
```json
{
    "user_id": "string",        // 用户ID，必填
    "conversation_id": "string" // 对话ID，必填
}
```

**响应格式**:

成功响应:
```json
{
    "success": true,
    "message": "对话删除成功",
    "user_id": "用户ID",
    "conversation_id": "对话ID"
}
```

失败响应:
```json
{
    "success": false,
    "message": "错误信息"
}
```

## 使用示例

### Python 示例

```python
import requests

# 删除对话
url = "http://localhost:8080/humanrelation/conversation"
data = {
    "user_id": "user123",
    "conversation_id": "conv456"
}

response = requests.delete(url, json=data)
result = response.json()

if result.get("success"):
    print("对话删除成功!")
else:
    print(f"删除失败: {result.get('message')}")
```

### curl 示例

```bash
curl -X DELETE "http://localhost:8080/humanrelation/conversation" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "conversation_id": "conv456"
  }'
```

## 错误处理

接口会进行以下验证：

1. **用户ID验证**: 用户ID不能为空或只包含空白字符
2. **对话ID验证**: 对话ID不能为空或只包含空白字符
3. **系统状态验证**: 检查系统是否正常初始化

常见错误信息：
- `"用户ID不能为空"` - 用户ID为空或无效
- `"对话ID不能为空"` - 对话ID为空或无效
- `"系统未初始化"` - 系统checkpointer未正确初始化
- `"对话不存在或删除失败"` - 指定的对话不存在或删除操作失败

## 数据删除范围

删除操作会清理以下数据：

1. **主对话记录**: `chat_history` 表中的对话记录
2. **档案快照**: `conversation_profile_snapshots` 表中的对话档案快照

## 安全考虑

- 接口确保用户隔离，只能删除属于指定用户的对话
- 删除操作不可逆，请谨慎使用
- 建议在删除前进行确认提示

## 测试

可以使用提供的测试脚本进行接口测试：

```bash
python test_delete_conversation.py
```

测试脚本会验证：
- 正常删除请求
- 无效参数处理
- 错误响应格式

## 日志记录

接口会记录以下日志：

- 删除操作开始: `开始删除对话: user_id=xxx, conversation_id=xxx`
- 删除成功: `用户 xxx 成功删除对话: xxx`
- 删除失败: `用户 xxx 删除对话失败，对话可能不存在: xxx`
- 异常错误: `删除对话时发生错误: user_id=xxx, conversation_id=xxx, error=xxx`

## 相关接口

- `GET /humanrelation/conversations` - 获取用户的所有会话列表
- `GET /humanrelation/history` - 获取指定对话的聊天记录
- `POST /humanrelation/create_conversation` - 创建新的对话
