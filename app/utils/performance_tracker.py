import time
import threading
from typing import Dict, List, Optional
from dataclasses import dataclass
from utils.logger import logger


@dataclass
class PerformanceEvent:
    """性能事件数据类"""
    timestamp: float
    event_name: str
    cumulative_ms: float
    extra_info: str = ""
    emoji: str = "📝"


class FirstPacketPerformanceTracker:
    """首包性能跟踪器 - 专注于首包发送前的关键性能节点"""

    def __init__(self, session_id: str, session_name: str = ""):
        self.session_id = session_id
        self.session_name = session_name
        self.start_time = time.time()
        self.events: List[PerformanceEvent] = []
        self.event_counter = 0
        self._lock = threading.Lock()
        self.is_finalized = False
        self.first_packet_sent = False

    def track_event(self, event_name: str, extra_info: str = "", emoji: str = "📝"):
        """跟踪事件（但不立即打印）"""
        if self.is_finalized or self.first_packet_sent:
            return

        with self._lock:
            current_time = time.time()
            cumulative_ms = (current_time - self.start_time) * 1000

            self.event_counter += 1
            event = PerformanceEvent(
                timestamp=current_time,
                event_name=event_name,
                cumulative_ms=cumulative_ms,
                extra_info=extra_info,
                emoji=emoji
            )
            self.events.append(event)

    def track_milestone(self, milestone_name: str, extra_info: str = "", emoji: str = "🎯"):
        """跟踪里程碑事件"""
        self.track_event(milestone_name, extra_info, emoji)

    def track_first_packet_sent(self):
        """标记首包已发送，并立即输出分析报告"""
        if self.first_packet_sent or self.is_finalized:
            return

        self.first_packet_sent = True
        self.track_milestone("首包发送", "首个AI内容片段已发送给用户", "🚀")

        # 立即输出首包性能分析报告
        self.finalize_and_print_first_packet_analysis()

    def finalize_and_print_first_packet_analysis(self):
        """完成跟踪并集中打印首包性能分析"""
        if self.is_finalized:
            return

        self.is_finalized = True

        # 计算总耗时
        total_time = (time.time() - self.start_time) * 1000

        # 集中打印首包性能分析报告
        logger.info("=" * 80)
        logger.info(f"🚀 [首包优化] 首包性能分析报告 - {self.session_name}")
        logger.info(f"📊 会话ID: {self.session_id}")
        logger.info(f"⏱️  首包总耗时: {total_time:.2f}ms")
        logger.info("-" * 80)

        # 分析关键性能节点
        key_milestones = []
        for i, event in enumerate(self.events, 1):
            if event.extra_info:
                logger.info(
                    f"{event.emoji} [首包优化] 事件 #{i}: {event.event_name}, "
                    f"累计耗时: {event.cumulative_ms:.2f}ms, {event.extra_info}"
                )
            else:
                logger.info(
                    f"{event.emoji} [首包优化] 事件 #{i}: {event.event_name}, "
                    f"累计耗时: {event.cumulative_ms:.2f}ms"
                )

            # 收集关键里程碑用于分析
            if event.emoji == "🎯":
                key_milestones.append((event.event_name, event.cumulative_ms))

        logger.info("-" * 80)

        # 性能分析总结
        if len(key_milestones) >= 2:
            logger.info("📈 [首包优化] 关键性能节点分析:")
            for i in range(1, len(key_milestones)):
                prev_name, prev_time = key_milestones[i-1]
                curr_name, curr_time = key_milestones[i]
                stage_time = curr_time - prev_time
                logger.info(f"   ⏱️  {prev_name} -> {curr_name}: {stage_time:.2f}ms")

        logger.info(f"🎯 [首包优化] 首包分析完成，总计 {len(self.events)} 个事件，首包耗时: {total_time:.2f}ms")
        logger.info("=" * 80)

    def get_first_packet_summary(self) -> Dict:
        """获取首包性能摘要（用于API返回）"""
        total_time = (time.time() - self.start_time) * 1000
        return {
            "session_id": self.session_id,
            "session_name": self.session_name,
            "first_packet_time_ms": total_time,
            "events_count": len(self.events),
            "events": [
                {
                    "id": i + 1,
                    "name": event.event_name,
                    "cumulative_ms": event.cumulative_ms,
                    "extra_info": event.extra_info,
                    "emoji": event.emoji
                } for i, event in enumerate(self.events)
            ]
        }


# 全局跟踪器存储（线程安全）
_trackers: Dict[str, FirstPacketPerformanceTracker] = {}
_trackers_lock = threading.Lock()


def get_performance_tracker(session_id: str, session_name: str = "") -> FirstPacketPerformanceTracker:
    """获取或创建性能跟踪器"""
    with _trackers_lock:
        if session_id not in _trackers:
            _trackers[session_id] = FirstPacketPerformanceTracker(session_id, session_name or session_id)
        return _trackers[session_id]


def cleanup_performance_tracker(session_id: str):
    """清理性能跟踪器"""
    with _trackers_lock:
        if session_id in _trackers:
            del _trackers[session_id]


# 便捷函数
def track_event(session_id: str, event_name: str, extra_info: str = "", emoji: str = "📝"):
    """便捷函数：跟踪事件"""
    tracker = get_performance_tracker(session_id)
    tracker.track_event(event_name, extra_info, emoji)


def track_milestone(session_id: str, milestone_name: str, extra_info: str = "", emoji: str = "🎯"):
    """便捷函数：跟踪里程碑"""
    tracker = get_performance_tracker(session_id)
    tracker.track_milestone(milestone_name, extra_info, emoji)


def track_first_packet_sent(session_id: str):
    """便捷函数：标记首包已发送"""
    tracker = get_performance_tracker(session_id)
    tracker.track_first_packet_sent()


def finalize_first_packet_analysis(session_id: str):
    """完成首包性能分析并打印报告"""
    with _trackers_lock:
        if session_id in _trackers:
            _trackers[session_id].finalize_and_print_first_packet_analysis()
